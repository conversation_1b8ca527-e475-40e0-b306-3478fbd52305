package co.com.gedsys.gedsys2bpmengine.controller.mapper;

import java.util.Map;

import org.cibseven.bpm.engine.task.Task;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import co.com.gedsys.gedsys2bpmengine.controller.dto.TaskDTO;

@Mapper(componentModel = "spring", 
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        builder = @org.mapstruct.Builder(disableBuilder = true))
public interface TaskMapper {
    
    TaskMapper INSTANCE = Mappers.getMapper(TaskMapper.class);
    
    @Mapping(target = "variables", source = "variables")
    TaskDTO toDTOWithVariables(Task task, Map<String, Object> variables);

    @Mapping(target = "variables", expression = "java(java.util.Collections.emptyMap())")
    TaskDTO toDTO(Task task);
}

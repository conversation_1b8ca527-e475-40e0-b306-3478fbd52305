# Modelo de CamundaForm y Deserialización de Componentes

## Introducción

Este documento describe el modelo de datos utilizado para representar formularios de Camunda en la aplicación, así como la solución implementada para resolver problemas de deserialización de componentes de formulario.

## Modelo de Datos

### CamundaForm

La clase `CamundaForm` es un registro Java que representa un formulario de Camunda con un identificador único y una lista de componentes.

```java
public record CamundaForm(  
    String id,  
    List<FormComponent> components  
) {}
```

### FormComponent

`FormComponent` es una interfaz que define los métodos comunes que deben implementar todos los componentes de formulario:

```java
public interface FormComponent {
    String getId();
    String getKey();
    String getLabel();
    String getDescription();
    ComponentType getType();
    Validate getValidate();
}
```

### AbstractFormComponent

`AbstractFormComponent` es una clase abstracta que implementa la interfaz `FormComponent` y proporciona funcionalidad común para todos los tipos de componentes:

```java
public abstract class AbstractFormComponent implements FormComponent {
    private final String id;
    private final String key;
    private final String label;
    private final String description;
    private final Validate validate;
    
    // Constructor y métodos getter
}
```

### Tipos de Componentes

El sistema soporta varios tipos de componentes de formulario, definidos en el enum `ComponentType`:

```java
public enum ComponentType {  
    select,  
    textfield,  
    textarea,  
    taglist,
    datetime
}
```

Cada tipo de componente tiene su propia clase de implementación que extiende `AbstractFormComponent`:

1. **TextFieldComponent**: Componente para campos de texto simple.
2. **TextAreaComponent**: Componente para áreas de texto multilínea.
3. **SelectComponent**: Componente para listas desplegables.
4. **TagListComponent**: Componente para listas de etiquetas.
5. **DateTimeComponent**: Componente para fechas y horas.

### Validación

La clase `Validate` define las reglas de validación para los componentes de formulario:

```java
public record Validate(  
    Boolean required,  
    Integer minLength,  
    Integer maxLength,  
    String validationType,  
    String pattern  
) {}
```

## Problema de Deserialización

El sistema enfrentaba un problema al deserializar JSON en objetos `FormComponent`. El error específico era:

```
"exceptionMessage": "Error al leer el formulario de inicio: Cannot construct instance of `co.com.gedsys.gedsys2bpmengine.model.FormComponent` (no Creators, like default constructor, exist): abstract types either need to be mapped to concrete types, have custom deserializer, or contain additional type information\n at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 3, column: 5] (through reference chain: co.com.gedsys.gedsys2bpmengine.model.CamundaForm[\"components\"]->java.util.ArrayList[0])"
```

Este error ocurría porque Jackson no puede deserializar directamente a una interfaz o clase abstracta sin información adicional sobre qué implementación concreta utilizar.

## Solución Implementada

### 1. Deserializador Personalizado

Se creó un deserializador personalizado (`FormComponentDeserializer`) que utiliza el campo "type" en el JSON para determinar qué implementación concreta de `FormComponent` crear:

```java
public class FormComponentDeserializer extends JsonDeserializer<FormComponent> {
    @Override
    public FormComponent deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        // Leer el nodo JSON
        ObjectMapper mapper = (ObjectMapper) jp.getCodec();
        ObjectNode root = mapper.readTree(jp);
        
        // Obtener el tipo para determinar qué clase concreta usar
        String typeStr = root.get("type").asText();
        ComponentType type = ComponentType.valueOf(typeStr);
        
        // Extraer campos comunes
        String id = getStringValue(root, "id");
        String key = getStringValue(root, "key");
        String label = getStringValue(root, "label");
        String description = getStringValue(root, "description");
        Validate validate = null;
        if (root.has("validate")) {
            validate = mapper.treeToValue(root.get("validate"), Validate.class);
        }
        
        // Crear el componente apropiado según el tipo
        switch (type) {
            case textfield:
                // Crear TextFieldComponent
            case textarea:
                // Crear TextAreaComponent
            case select:
                // Crear SelectComponent
            case taglist:
                // Crear TagListComponent
            case datetime:
                // Crear DateTimeComponent
            default:
                throw new IOException("Tipo de componente no soportado: " + type);
        }
    }
}
```

### 2. Configuración de Jackson

Se actualizó la configuración de Jackson para registrar el deserializador personalizado:

```java
@Configuration
public class JacksonConfig {
    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        
        // Configuración básica
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.registerModule(new JavaTimeModule());
        
        // Registrar deserializador personalizado para la interfaz FormComponent
        SimpleModule formComponentModule = new SimpleModule();
        formComponentModule.addDeserializer(FormComponent.class, new FormComponentDeserializer());
        mapper.registerModule(formComponentModule);
        
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.LOWER_CAMEL_CASE);
        
        return mapper;
    }
}
```

## Pruebas

Se crearon pruebas unitarias para verificar que la deserialización funciona correctamente para diferentes tipos de componentes:

1. Prueba de deserialización de un formulario con un componente TextField
2. Prueba de deserialización de un formulario con un componente Select
3. Prueba de deserialización de un formulario con múltiples componentes de diferentes tipos

Las pruebas confirmaron que la solución implementada resuelve el problema de deserialización.

## Conclusión

La implementación de un deserializador personalizado para la interfaz `FormComponent` permite que el sistema deserialice correctamente JSON en objetos de formulario de Camunda con diferentes tipos de componentes. Esta solución es robusta y extensible, permitiendo añadir fácilmente nuevos tipos de componentes en el futuro.
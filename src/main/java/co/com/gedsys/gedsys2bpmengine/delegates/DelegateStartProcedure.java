package co.com.gedsys.gedsys2bpmengine.delegates;

import co.com.gedsys.gedsys2bpmengine.constant.TaskVariableName;
import co.com.gedsys.gedsys2bpmengine.datastructucture.StartProcedure;
import co.com.gedsys.gedsys2bpmengine.infrastructure.config.ApplicationConfigProperties;

import org.cibseven.bpm.engine.delegate.DelegateExecution;
import org.cibseven.bpm.engine.delegate.ExecutionListener;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

@Component("startProcedure")
@EnableConfigurationProperties(ApplicationConfigProperties.class)
public class DelegateStartProcedure implements ExecutionListener {
    private final ApplicationConfigProperties appConfig;
    private final AmqpTemplate amqpTemplate;

    public DelegateStartProcedure(
            ApplicationConfigProperties appConfig,
            @Qualifier("secondary") AmqpTemplate amqpTemplate) {
        this.appConfig = appConfig;
        this.amqpTemplate = amqpTemplate;
    }

    @Override
    public void notify(DelegateExecution delegateExecution) {
        try {
            Map<String, Object> localVariables = delegateExecution.getVariablesLocal();
            validateRequiredVariables(localVariables);

            var procedureKey = (String) localVariables.get(TaskVariableName.PROCEDURE_KEY);
            var tenantId = appConfig.tenantId();
            var createdAt = LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME);
            var processInstanceId = delegateExecution.getProcessInstanceId();
            var executionId = delegateExecution.getId();
            var correlationMessage = (String) localVariables.get(TaskVariableName.CORRELATION_MESSAGE);
            var procedure = new StartProcedure(processInstanceId, executionId, correlationMessage, procedureKey,
                    tenantId, createdAt, localVariables);

            amqpTemplate.convertAndSend("procedure." + procedureKey, procedure);

        } catch (ServiceTaskValidationException e) {
            delegateExecution.createIncident(
                    "ServiceTaskHandler",
                    "Error de validación en variables requeridas",
                    e.getMessage());
        } catch (Exception e) {
            delegateExecution.createIncident(
                    "ServiceTaskHandler",
                    "Error al publicar el proceso",
                    e.getMessage());
        }
    }

    private void validateRequiredVariables(Map<String, Object> variables) {
        if (!variables.containsKey(TaskVariableName.PROCEDURE_KEY)
                || variables.get(TaskVariableName.PROCEDURE_KEY) == null) {
            throw new ServiceTaskValidationException("La variable 'procedureKey' es requerida y no fue encontrada");
        }
    }

    private static class ServiceTaskValidationException extends RuntimeException {
        public ServiceTaskValidationException(String message) {
            super(message);
        }
    }
}

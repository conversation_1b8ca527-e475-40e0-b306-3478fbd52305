<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0r8v8er" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.23.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.21.0">
  <bpmn:process id="first" name="Primer proceso" isExecutable="true" camunda:historyTimeToLive="123">
    <bpmn:startEvent id="Event_0f0ylas">
      <bpmn:outgoing>Flow_0hnix9h</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="envio_1pxspnm" name="Completar Información de Envío" camunda:formRef="formularioEnvioEmail" camunda:formRefBinding="latest" camunda:assignee="${initiator}">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:outputParameter name="envioSubmission">${envioSubmission}</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0hnix9h</bpmn:incoming>
      <bpmn:outgoing>Flow_1j7awto</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0hnix9h" sourceRef="Event_0f0ylas" targetRef="envio_1pxspnm" />
    <bpmn:sequenceFlow id="Flow_1j7awto" sourceRef="envio_1pxspnm" targetRef="Activity_0e31sx3" />
    <bpmn:userTask id="Activity_0e31sx3" name="verificar" camunda:formKey="fake">
      <bpmn:incoming>Flow_1j7awto</bpmn:incoming>
      <bpmn:outgoing>Flow_0yibiny</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="Event_022qggh">
      <bpmn:incoming>Flow_0yibiny</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0yibiny" sourceRef="Activity_0e31sx3" targetRef="Event_022qggh" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="first">
      <bpmndi:BPMNShape id="Event_0f0ylas_di" bpmnElement="Event_0f0ylas">
        <dc:Bounds x="152" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_16li34c_di" bpmnElement="envio_1pxspnm">
        <dc:Bounds x="280" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1gxfcnk_di" bpmnElement="Activity_0e31sx3">
        <dc:Bounds x="480" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_022qggh_di" bpmnElement="Event_022qggh">
        <dc:Bounds x="682" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0hnix9h_di" bpmnElement="Flow_0hnix9h">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="280" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1j7awto_di" bpmnElement="Flow_1j7awto">
        <di:waypoint x="380" y="120" />
        <di:waypoint x="480" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yibiny_di" bpmnElement="Flow_0yibiny">
        <di:waypoint x="580" y="120" />
        <di:waypoint x="682" y="120" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>

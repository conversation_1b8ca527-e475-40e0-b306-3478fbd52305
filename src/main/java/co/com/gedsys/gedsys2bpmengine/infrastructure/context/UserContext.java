package co.com.gedsys.gedsys2bpmengine.infrastructure.context;

/**
 * Clase para almacenar información del usuario actual en el contexto de la aplicación.
 * Permite acceder a los headers X-Username y X-Group desde cualquier parte de la aplicación.
 */
public class UserContext {
    private static final ThreadLocal<String> username = new ThreadLocal<>();

    /**
     * Establece el nombre de usuario en el contexto actual
     * @param user nombre de usuario
     */
    public static void setUsername(String user) {
        username.set(user);
    }


    /**
     * Obtiene el nombre de usuario del contexto actual
     * @return nombre de usuario
     */
    public static String getUsername() {
        return username.get();
    }

    /**
     * Limpia los datos del contexto actual
     */
    public static void clear() {
        username.remove();
    }
}

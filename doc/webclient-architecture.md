# Arquitectura para un WebClient Reutilizable y Robusto

Este documento describe la arquitectura para un WebClient reutilizable y robusto en Java utilizando Spring WebClient, diseñado para consumir servicios desplegados en contenedores dentro de la misma red.

## Diagrama Conceptual

```mermaid
classDiagram
    class WebClientConfig {
        +String baseUrl
        +Duration connectTimeout
        +Duration readTimeout
        +Map~String, String~ defaultHeaders
        +ObjectMapper objectMapper
        +WebClientConfig build()
    }
    
    class WebClientBuilder {
        +WebClientBuilder baseUrl(String baseUrl)
        +WebClientBuilder connectTimeout(Duration timeout)
        +WebClientBuilder readTimeout(Duration timeout)
        +WebClientBuilder defaultHeader(String name, String value)
        +WebClientBuilder objectMapper(ObjectMapper mapper)
        +WebClientBuilder enableLogging(boolean enable)
        +WebClientBuilder logLevel(LogLevel level)
        +GedsysWebClient build()
    }
    
    class GedsysWebClient {
        -WebClient webClient
        -WebClientConfig config
        -WebClientLogger logger
        +<T> Mono~T~ get(String path, Class<T> responseType)
        +<T> Mono~T~ get(String path, ParameterizedTypeReference<T> responseType)
        +<T, R> Mono~R~ post(String path, T body, Class<R> responseType)
        +<T, R> Mono~R~ post(String path, T body, ParameterizedTypeReference<R> responseType)
        +<T, R> Mono~R~ put(String path, T body, Class<R> responseType)
        +<T, R> Mono~R~ put(String path, T body, ParameterizedTypeReference<R> responseType)
        +<T> Mono~T~ delete(String path, Class<T> responseType)
        +<T> Mono~T~ delete(String path, ParameterizedTypeReference<T> responseType)
    }
    
    class WebClientLogger {
        -LogLevel logLevel
        +void logRequest(ClientRequest request)
        +void logResponse(ClientResponse response)
        +void logError(Throwable error)
    }
    
    class WebClientException {
        -HttpStatus status
        -String path
        -String method
        -String errorBody
        -Throwable cause
        +HttpStatus getStatus()
        +String getPath()
        +String getMethod()
        +String getErrorBody()
        +Throwable getCause()
    }
    
    enum LogLevel {
        NONE
        BASIC
        HEADERS
        BODY
        ALL
    }
    
    WebClientBuilder --> GedsysWebClient : builds
    WebClientBuilder --> WebClientConfig : creates
    GedsysWebClient --> WebClientConfig : uses
    GedsysWebClient --> WebClientLogger : uses
    GedsysWebClient --> WebClientException : throws
    WebClientLogger --> LogLevel : uses
```

## Descripción de Componentes

### 1. WebClientConfig

Esta clase inmutable contiene toda la configuración necesaria para crear un WebClient.

**Responsabilidades:**
- Almacenar la URL base para las peticiones
- Configurar timeouts de conexión y lectura
- Definir headers por defecto
- Configurar el ObjectMapper para serialización/deserialización

### 2. WebClientBuilder

Implementa el patrón Builder para facilitar la creación y configuración de instancias de GedsysWebClient.

**Responsabilidades:**
- Proporcionar una API fluida para configurar el WebClient
- Validar la configuración antes de crear el WebClient
- Crear y configurar el WebClient subyacente de Spring
- Configurar el logging según las preferencias

### 3. GedsysWebClient

La clase principal que encapsula el WebClient de Spring y proporciona métodos simplificados para realizar peticiones HTTP.

**Responsabilidades:**
- Ejecutar peticiones HTTP (GET, POST, PUT, DELETE)
- Manejar la serialización/deserialización de objetos
- Registrar peticiones y respuestas a través del logger
- Transformar excepciones en WebClientException con información detallada
- Aplicar la configuración definida (timeouts, headers, etc.)

### 4. WebClientLogger

Componente responsable de registrar información detallada sobre peticiones, respuestas y errores.

**Responsabilidades:**
- Registrar detalles de las peticiones (URL, método, headers, cuerpo)
- Registrar detalles de las respuestas (código de estado, headers, cuerpo)
- Registrar información detallada sobre errores
- Aplicar diferentes niveles de logging según la configuración

### 5. WebClientException

Excepción personalizada que proporciona información detallada sobre errores HTTP.

**Responsabilidades:**
- Capturar el código de estado HTTP
- Almacenar la URL y el método de la petición que falló
- Capturar el cuerpo de la respuesta de error
- Mantener la causa original de la excepción

### 6. LogLevel

Enumeración que define los diferentes niveles de detalle para el logging.

**Valores:**
- NONE: Sin logging
- BASIC: Solo información básica (método, URL, código de estado)
- HEADERS: Información básica + headers
- BODY: Información básica + headers + cuerpo (truncado si es muy grande)
- ALL: Logging completo y detallado

## Interacciones entre Componentes

1. **Configuración y Creación:**
   - El cliente utiliza WebClientBuilder para configurar y crear una instancia de GedsysWebClient
   - WebClientBuilder crea un objeto WebClientConfig inmutable
   - WebClientBuilder configura el WebClient de Spring con los parámetros especificados
   - WebClientBuilder crea y configura el WebClientLogger según las preferencias

2. **Ejecución de Peticiones:**
   - El cliente llama a un método de GedsysWebClient (get, post, put, delete)
   - GedsysWebClient prepara la petición con la URL base, headers por defecto, etc.
   - WebClientLogger registra los detalles de la petición
   - GedsysWebClient ejecuta la petición a través del WebClient de Spring
   - WebClientLogger registra los detalles de la respuesta o error
   - GedsysWebClient transforma la respuesta o error y la devuelve al cliente

3. **Manejo de Errores:**
   - Si ocurre un error, GedsysWebClient captura la excepción
   - GedsysWebClient extrae información detallada del error
   - WebClientLogger registra los detalles del error
   - GedsysWebClient crea y lanza una WebClientException con la información detallada
   - El cliente captura y maneja la WebClientException según sus necesidades

## Ejemplo de Implementación

### WebClientConfig

```java
@Getter
@Builder
public class WebClientConfig {
    private final String baseUrl;
    private final Duration connectTimeout;
    private final Duration readTimeout;
    private final Map<String, String> defaultHeaders;
    private final ObjectMapper objectMapper;
    private final LogLevel logLevel;
    
    // Constructor privado para usar con el Builder
    private WebClientConfig(String baseUrl, Duration connectTimeout, Duration readTimeout,
                           Map<String, String> defaultHeaders, ObjectMapper objectMapper,
                           LogLevel logLevel) {
        this.baseUrl = baseUrl;
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
        this.defaultHeaders = Collections.unmodifiableMap(
            defaultHeaders != null ? new HashMap<>(defaultHeaders) : new HashMap<>());
        this.objectMapper = objectMapper != null ? objectMapper : new ObjectMapper();
        this.logLevel = logLevel != null ? logLevel : LogLevel.BASIC;
    }
    
    // Builder estático para facilitar la creación
    public static WebClientConfigBuilder builder() {
        return new WebClientConfigBuilder();
    }
    
    // Clase Builder interna
    public static class WebClientConfigBuilder {
        private String baseUrl;
        private Duration connectTimeout = Duration.ofSeconds(5);
        private Duration readTimeout = Duration.ofSeconds(30);
        private Map<String, String> defaultHeaders = new HashMap<>();
        private ObjectMapper objectMapper;
        private LogLevel logLevel = LogLevel.BASIC;
        
        public WebClientConfigBuilder baseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
            return this;
        }
        
        public WebClientConfigBuilder connectTimeout(Duration timeout) {
            this.connectTimeout = timeout;
            return this;
        }
        
        public WebClientConfigBuilder readTimeout(Duration timeout) {
            this.readTimeout = timeout;
            return this;
        }
        
        public WebClientConfigBuilder defaultHeader(String name, String value) {
            this.defaultHeaders.put(name, value);
            return this;
        }
        
        public WebClientConfigBuilder objectMapper(ObjectMapper mapper) {
            this.objectMapper = mapper;
            return this;
        }
        
        public WebClientConfigBuilder logLevel(LogLevel level) {
            this.logLevel = level;
            return this;
        }
        
        public WebClientConfig build() {
            if (baseUrl == null || baseUrl.isEmpty()) {
                throw new IllegalArgumentException("baseUrl cannot be null or empty");
            }
            
            return new WebClientConfig(baseUrl, connectTimeout, readTimeout, 
                                      defaultHeaders, objectMapper, logLevel);
        }
    }
}
```

### LogLevel

```java
public enum LogLevel {
    NONE,
    BASIC,
    HEADERS,
    BODY,
    ALL
}
```

### WebClientLogger

```java
@Slf4j
public class WebClientLogger {
    private final LogLevel logLevel;
    
    public WebClientLogger(LogLevel logLevel) {
        this.logLevel = logLevel != null ? logLevel : LogLevel.BASIC;
    }
    
    public void logRequest(ClientRequest request) {
        if (logLevel == LogLevel.NONE) return;
        
        StringBuilder message = new StringBuilder();
        message.append("\n============================= REQUEST =============================\n");
        message.append(String.format("HTTP %s %s\n", request.method(), request.url()));
        
        if (logLevel.ordinal() >= LogLevel.HEADERS.ordinal()) {
            message.append("HEADERS:\n");
            request.headers().forEach((name, values) -> {
                values.forEach(value -> message.append(String.format("%s: %s\n", name, value)));
            });
        }
        
        if (logLevel.ordinal() >= LogLevel.BODY.ordinal() && request.body() != null) {
            message.append("BODY:\n");
            try {
                // Intentar obtener el cuerpo de la petición
                // Esto es simplificado, en una implementación real sería más complejo
                message.append("[Body content not available in request logging]\n");
            } catch (Exception e) {
                message.append("[Error reading request body]\n");
            }
        }
        
        message.append("==================================================================\n");
        log.info(message.toString());
    }
    
    public void logResponse(ClientResponse response) {
        if (logLevel == LogLevel.NONE) return;
        
        StringBuilder message = new StringBuilder();
        message.append("\n============================= RESPONSE ============================\n");
        message.append(String.format("STATUS: %s\n", response.statusCode()));
        
        if (logLevel.ordinal() >= LogLevel.HEADERS.ordinal()) {
            message.append("HEADERS:\n");
            response.headers().asHttpHeaders().forEach((name, values) -> {
                values.forEach(value -> message.append(String.format("%s: %s\n", name, value)));
            });
        }
        
        // El cuerpo de la respuesta no se puede registrar aquí directamente
        // porque consumiría el flujo. En una implementación real, se necesitaría
        // una estrategia más sofisticada.
        
        message.append("==================================================================\n");
        log.info(message.toString());
    }
    
    public void logResponseBody(String body) {
        if (logLevel.ordinal() < LogLevel.BODY.ordinal() || body == null) return;
        
        StringBuilder message = new StringBuilder();
        message.append("\n========================= RESPONSE BODY ==========================\n");
        
        // Limitar el tamaño del cuerpo para evitar logs demasiado grandes
        String truncatedBody = body.length() > 10000 
            ? body.substring(0, 10000) + "... [TRUNCATED]" 
            : body;
        
        message.append(truncatedBody);
        message.append("\n==================================================================\n");
        log.info(message.toString());
    }
    
    public void logError(Throwable error) {
        if (logLevel == LogLevel.NONE) return;
        
        StringBuilder message = new StringBuilder();
        message.append("\n============================= ERROR ==============================\n");
        message.append(String.format("ERROR TYPE: %s\n", error.getClass().getName()));
        message.append(String.format("ERROR MESSAGE: %s\n", error.getMessage()));
        
        if (logLevel == LogLevel.ALL) {
            message.append("STACK TRACE:\n");
            StringWriter sw = new StringWriter();
            error.printStackTrace(new PrintWriter(sw));
            message.append(sw.toString());
        }
        
        message.append("==================================================================\n");
        log.error(message.toString());
    }
}
```

### WebClientException

```java
public class WebClientException extends RuntimeException {
    private final HttpStatus status;
    private final String path;
    private final String method;
    private final String errorBody;
    
    public WebClientException(HttpStatus status, String path, String method, 
                             String errorBody, Throwable cause) {
        super(formatMessage(status, path, method), cause);
        this.status = status;
        this.path = path;
        this.method = method;
        this.errorBody = errorBody;
    }
    
    private static String formatMessage(HttpStatus status, String path, String method) {
        return String.format("Error executing %s request to %s: %s %s", 
                            method, path, status.value(), status.getReasonPhrase());
    }
    
    public HttpStatus getStatus() {
        return status;
    }
    
    public String getPath() {
        return path;
    }
    
    public String getMethod() {
        return method;
    }
    
    public String getErrorBody() {
        return errorBody;
    }
}
```

### GedsysWebClient

```java
public class GedsysWebClient {
    private final WebClient webClient;
    private final WebClientConfig config;
    private final WebClientLogger logger;
    
    private GedsysWebClient(WebClientConfig config, WebClient webClient, WebClientLogger logger) {
        this.config = config;
        this.webClient = webClient;
        this.logger = logger;
    }
    
    public static GedsysWebClientBuilder builder() {
        return new GedsysWebClientBuilder();
    }
    
    public <T> Mono<T> get(String path, Class<T> responseType) {
        return executeRequest(HttpMethod.GET, path, null, responseType);
    }
    
    public <T> Mono<T> get(String path, ParameterizedTypeReference<T> responseType) {
        return executeRequest(HttpMethod.GET, path, null, responseType);
    }
    
    public <T, R> Mono<R> post(String path, T body, Class<R> responseType) {
        return executeRequest(HttpMethod.POST, path, body, responseType);
    }
    
    public <T, R> Mono<R> post(String path, T body, ParameterizedTypeReference<R> responseType) {
        return executeRequest(HttpMethod.POST, path, body, responseType);
    }
    
    public <T, R> Mono<R> put(String path, T body, Class<R> responseType) {
        return executeRequest(HttpMethod.PUT, path, body, responseType);
    }
    
    public <T, R> Mono<R> put(String path, T body, ParameterizedTypeReference<R> responseType) {
        return executeRequest(HttpMethod.PUT, path, body, responseType);
    }
    
    public <T> Mono<T> delete(String path, Class<T> responseType) {
        return executeRequest(HttpMethod.DELETE, path, null, responseType);
    }
    
    public <T> Mono<T> delete(String path, ParameterizedTypeReference<T> responseType) {
        return executeRequest(HttpMethod.DELETE, path, null, responseType);
    }
    
    private <T, R> Mono<R> executeRequest(HttpMethod method, String path, T body, Class<R> responseType) {
        return webClient
            .method(method)
            .uri(path)
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(body != null ? body : "")
            .retrieve()
            .onStatus(HttpStatus::isError, response -> handleErrorResponse(method, path, response))
            .bodyToMono(responseType)
            .doOnError(error -> handleError(method, path, error));
    }
    
    private <T, R> Mono<R> executeRequest(HttpMethod method, String path, T body, 
                                         ParameterizedTypeReference<R> responseType) {
        return webClient
            .method(method)
            .uri(path)
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(body != null ? body : "")
            .retrieve()
            .onStatus(HttpStatus::isError, response -> handleErrorResponse(method, path, response))
            .bodyToMono(responseType)
            .doOnError(error -> handleError(method, path, error));
    }
    
    private Mono<? extends Throwable> handleErrorResponse(HttpMethod method, String path, 
                                                         ClientResponse response) {
        return response.bodyToMono(String.class)
            .defaultIfEmpty("")
            .flatMap(errorBody -> {
                logger.logResponseBody(errorBody);
                return Mono.error(new WebClientException(
                    response.statusCode(), 
                    path, 
                    method.name(), 
                    errorBody,
                    null
                ));
            });
    }
    
    private void handleError(HttpMethod method, String path, Throwable error) {
        logger.logError(error);
        
        // Si ya es un WebClientException, no lo envolvemos de nuevo
        if (error instanceof WebClientException) {
            return;
        }
        
        // Para otros errores, los envolvemos en un WebClientException
        throw new WebClientException(
            HttpStatus.INTERNAL_SERVER_ERROR,
            path,
            method.name(),
            error.getMessage(),
            error
        );
    }
    
    public static class GedsysWebClientBuilder {
        private String baseUrl;
        private Duration connectTimeout = Duration.ofSeconds(5);
        private Duration readTimeout = Duration.ofSeconds(30);
        private Map<String, String> defaultHeaders = new HashMap<>();
        private ObjectMapper objectMapper;
        private LogLevel logLevel = LogLevel.BASIC;
        
        public GedsysWebClientBuilder baseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
            return this;
        }
        
        public GedsysWebClientBuilder connectTimeout(Duration timeout) {
            this.connectTimeout = timeout;
            return this;
        }
        
        public GedsysWebClientBuilder readTimeout(Duration timeout) {
            this.readTimeout = timeout;
            return this;
        }
        
        public GedsysWebClientBuilder defaultHeader(String name, String value) {
            this.defaultHeaders.put(name, value);
            return this;
        }
        
        public GedsysWebClientBuilder objectMapper(ObjectMapper mapper) {
            this.objectMapper = mapper;
            return this;
        }
        
        public GedsysWebClientBuilder logLevel(LogLevel level) {
            this.logLevel = level;
            return this;
        }
        
        public GedsysWebClient build() {
            WebClientConfig config = WebClientConfig.builder()
                .baseUrl(baseUrl)
                .connectTimeout(connectTimeout)
                .readTimeout(readTimeout)
                .objectMapper(objectMapper)
                .logLevel(logLevel)
                .build();
            
            // Configurar el ObjectMapper
            ObjectMapper mapper = config.getObjectMapper();
            
            // Configurar el ExchangeStrategies para usar el ObjectMapper personalizado
            ExchangeStrategies exchangeStrategies = ExchangeStrategies.builder()
                .codecs(configurer -> {
                    configurer.defaultCodecs().jackson2JsonEncoder(
                        new Jackson2JsonEncoder(mapper, MediaType.APPLICATION_JSON));
                    configurer.defaultCodecs().jackson2JsonDecoder(
                        new Jackson2JsonDecoder(mapper, MediaType.APPLICATION_JSON));
                })
                .build();
            
            // Crear el logger
            WebClientLogger logger = new WebClientLogger(config.getLogLevel());
            
            // Crear y configurar el WebClient
            WebClient webClient = WebClient.builder()
                .baseUrl(config.getBaseUrl())
                .defaultHeaders(headers -> {
                    config.getDefaultHeaders().forEach(headers::add);
                })
                .clientConnector(new ReactorClientHttpConnector(
                    HttpClient.create()
                        .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 
                               (int) config.getConnectTimeout().toMillis())
                        .responseTimeout(config.getReadTimeout())
                ))
                .exchangeStrategies(exchangeStrategies)
                .filter((request, next) -> {
                    logger.logRequest(request);
                    return next.exchange(request)
                        .doOnNext(logger::logResponse);
                })
                .build();
            
            return new GedsysWebClient(config, webClient, logger);
        }
    }
}
```

## Ejemplo de Uso

```java
// Crear un cliente con configuración básica
GedsysWebClient client = GedsysWebClient.builder()
    .baseUrl("http://servicio-interno")
    .connectTimeout(Duration.ofSeconds(3))
    .readTimeout(Duration.ofSeconds(10))
    .defaultHeader("Content-Type", "application/json")
    .defaultHeader("Accept", "application/json")
    .logLevel(LogLevel.BODY)
    .build();

// Realizar una petición GET
client.get("/api/recursos", Recurso.class)
    .subscribe(
        recurso -> System.out.println("Recurso obtenido: " + recurso),
        error -> {
            if (error instanceof WebClientException) {
                WebClientException wce = (WebClientException) error;
                System.err.println("Error HTTP: " + wce.getStatus());
                System.err.println("Cuerpo del error: " + wce.getErrorBody());
            } else {
                System.err.println("Error inesperado: " + error.getMessage());
            }
        }
    );

// Realizar una petición POST
Solicitud solicitud = new Solicitud("datos", 123);
client.post("/api/solicitudes", solicitud, Respuesta.class)
    .subscribe(
        respuesta -> System.out.println("Respuesta recibida: " + respuesta),
        error -> {
            if (error instanceof WebClientException) {
                WebClientException wce = (WebClientException) error;
                System.err.println("Error HTTP: " + wce.getStatus());
                System.err.println("Cuerpo del error: " + wce.getErrorBody());
            } else {
                System.err.println("Error inesperado: " + error.getMessage());
            }
        }
    );
```

## Configuración en Spring Boot

Para facilitar la integración con Spring Boot, podemos crear una configuración automática:

```java
@Configuration
@ConditionalOnClass(WebClient.class)
public class GedsysWebClientAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public GedsysWebClient gedsysWebClient(
            @Value("${gedsys.webclient.base-url:}") String baseUrl,
            @Value("${gedsys.webclient.connect-timeout:5s}") Duration connectTimeout,
            @Value("${gedsys.webclient.read-timeout:30s}") Duration readTimeout,
            @Value("${gedsys.webclient.log-level:BASIC}") String logLevel) {
        
        return GedsysWebClient.builder()
            .baseUrl(baseUrl)
            .connectTimeout(connectTimeout)
            .readTimeout(readTimeout)
            .logLevel(LogLevel.valueOf(logLevel))
            .build();
    }
}
```

## Conclusiones y Beneficios

La arquitectura propuesta ofrece varios beneficios:

1. **Reutilización**: El diseño modular permite reutilizar el cliente en diferentes proyectos.

2. **Configurabilidad**: Todos los aspectos importantes (URL base, timeouts, headers, serialización) son configurables.

3. **Logging Detallado**: El componente de logging proporciona información detallada para depuración.

4. **Manejo de Errores**: La excepción personalizada proporciona información detallada sobre los errores.

5. **Simplicidad de Uso**: La API fluida y los métodos simplificados facilitan el uso del cliente.

6. **Extensibilidad**: El diseño permite añadir nuevas funcionalidades en el futuro si es necesario.

7. **Integración con Spring Boot**: La configuración automática facilita la integración con aplicaciones Spring Boot.

## Pasos para la Implementación

1. Añadir la dependencia de Spring WebFlux al proyecto:
   ```xml
   <dependency>
       <groupId>org.springframework.boot</groupId>
       <artifactId>spring-boot-starter-webflux</artifactId>
   </dependency>
   ```

2. Crear los paquetes necesarios:
   ```
   co.com.gedsys.gedsys2bpmengine.infrastructure.webclient
   ```

3. Implementar las clases descritas en este documento:
   - LogLevel
   - WebClientConfig
   - WebClientLogger
   - WebClientException
   - GedsysWebClient
   - GedsysWebClientAutoConfiguration

4. Configurar las propiedades en application.yml:
   ```yaml
   gedsys:
     webclient:
       base-url: http://servicio-interno
       connect-timeout: 5s
       read-timeout: 30s
       log-level: BODY
   ```

5. Utilizar el cliente en los servicios que necesiten realizar peticiones HTTP.
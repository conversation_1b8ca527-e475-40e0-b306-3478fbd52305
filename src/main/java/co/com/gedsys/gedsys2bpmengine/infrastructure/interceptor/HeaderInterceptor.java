package co.com.gedsys.gedsys2bpmengine.infrastructure.interceptor;

import co.com.gedsys.gedsys2bpmengine.infrastructure.context.UserContext;
import co.com.gedsys.gedsys2bpmengine.infrastructure.exception.HeaderMissingException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * Interceptor para capturar los headers X-Username y X-Group en las solicitudes.
 */
@Component
public class HeaderInterceptor implements HandlerInterceptor {

    private static final String USERNAME_HEADER = "X-Username";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String username = request.getHeader(USERNAME_HEADER);

        // Verificar que ambos headers estén presentes
        if (username == null || username.isEmpty()) {
            throw new HeaderMissingException("El header X-Username es requerido");
        }

        // Almacenar los valores en el contexto
        UserContext.setUsername(username);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // Limpiar el contexto después de que se complete la solicitud
        UserContext.clear();
    }
}

package co.com.gedsys.gedsys2bpmengine.infrastructure.config;

import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
@EnableConfigurationProperties(RabbitMQVirtualHostProperties.class)
public class MultiVirtualHostRabbitMQConfig {
    
    @Bean
    @Primary
    public ConnectionFactory primaryConnectionFactory(RabbitProperties rabbitProperties) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(rabbitProperties.getHost());
        connectionFactory.setPort(rabbitProperties.getPort());
        connectionFactory.setUsername(rabbitProperties.getUsername());
        connectionFactory.setPassword(rabbitProperties.getPassword());
        connectionFactory.setVirtualHost(rabbitProperties.getVirtualHost());
        
        configureConnectionFactory(connectionFactory, rabbitProperties);
        
        return connectionFactory;
    }
    
    @Bean
    public ConnectionFactory secondaryConnectionFactory(
            RabbitProperties rabbitProperties,
            RabbitMQVirtualHostProperties vhostProperties) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(rabbitProperties.getHost());
        connectionFactory.setPort(rabbitProperties.getPort());
        connectionFactory.setUsername(rabbitProperties.getUsername());
        connectionFactory.setPassword(rabbitProperties.getPassword());
        connectionFactory.setVirtualHost(vhostProperties.secondaryVirtualHost());
        
        configureConnectionFactory(connectionFactory, rabbitProperties);
        
        return connectionFactory;
    }
    
    @Bean
    @Primary
    public AmqpTemplate primaryAmqpTemplate(
            @Qualifier("primaryConnectionFactory") ConnectionFactory connectionFactory,
            MessageConverter messageConverter) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(messageConverter);
        return template;
    }
    
    @Bean
    @Qualifier("secondary")
    public AmqpTemplate secondaryAmqpTemplate(
            @Qualifier("secondaryConnectionFactory") ConnectionFactory connectionFactory,
            MessageConverter messageConverter) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(messageConverter);
        return template;
    }
    
    @Bean
    @Primary
    public SimpleRabbitListenerContainerFactory primaryRabbitListenerContainerFactory(
            @Qualifier("primaryConnectionFactory") ConnectionFactory connectionFactory,
            MessageConverter messageConverter,
            RabbitProperties rabbitProperties) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(messageConverter);
        
        // Configurar propiedades del listener desde application.yml
        if (rabbitProperties.getListener() != null && rabbitProperties.getListener().getSimple() != null) {
            var simpleProperties = rabbitProperties.getListener().getSimple();
            factory.setAcknowledgeMode(simpleProperties.getAcknowledgeMode());
            factory.setDefaultRequeueRejected(simpleProperties.getDefaultRequeueRejected());
            
            if (simpleProperties.getRetry() != null && simpleProperties.getRetry().isEnabled()) {
                factory.setRetryTemplate(createRetryTemplate(simpleProperties.getRetry()));
            }
        }
        
        return factory;
    }
    
    @Bean
    @Qualifier("secondary")
    public SimpleRabbitListenerContainerFactory secondaryRabbitListenerContainerFactory(
            @Qualifier("secondaryConnectionFactory") ConnectionFactory connectionFactory,
            MessageConverter messageConverter,
            RabbitProperties rabbitProperties) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(messageConverter);
        
        // Configurar propiedades del listener desde application.yml
        if (rabbitProperties.getListener() != null && rabbitProperties.getListener().getSimple() != null) {
            var simpleProperties = rabbitProperties.getListener().getSimple();
            factory.setAcknowledgeMode(simpleProperties.getAcknowledgeMode());
            factory.setDefaultRequeueRejected(simpleProperties.getDefaultRequeueRejected());
            
            if (simpleProperties.getRetry() != null && simpleProperties.getRetry().isEnabled()) {
                factory.setRetryTemplate(createRetryTemplate(simpleProperties.getRetry()));
            }
        }
        
        return factory;
    }
    
    private void configureConnectionFactory(CachingConnectionFactory connectionFactory, RabbitProperties properties) {
        if (properties.getCache() != null && properties.getCache().getChannel() != null) {
            Integer channelSize = properties.getCache().getChannel().getSize();
            if (channelSize != null) {
                connectionFactory.setChannelCacheSize(channelSize);
            }
            if (properties.getCache().getChannel().getCheckoutTimeout() != null) {
                connectionFactory.setChannelCheckoutTimeout(properties.getCache().getChannel().getCheckoutTimeout().toMillis());
            }
        }
        
        if (properties.getCache() != null && properties.getCache().getConnection() != null) {
            if (properties.getCache().getConnection().getMode() != null) {
                connectionFactory.setCacheMode(properties.getCache().getConnection().getMode());
            }
            Integer connectionSize = properties.getCache().getConnection().getSize();
            if (connectionSize != null) {
                connectionFactory.setConnectionCacheSize(connectionSize);
            }
        }
    }
    
    private org.springframework.retry.support.RetryTemplate createRetryTemplate(
            org.springframework.boot.autoconfigure.amqp.RabbitProperties.ListenerRetry retryProperties) {
        org.springframework.retry.support.RetryTemplate retryTemplate = new org.springframework.retry.support.RetryTemplate();
        
        org.springframework.retry.policy.SimpleRetryPolicy retryPolicy = new org.springframework.retry.policy.SimpleRetryPolicy();
        retryPolicy.setMaxAttempts(retryProperties.getMaxAttempts());
        retryTemplate.setRetryPolicy(retryPolicy);
        
        org.springframework.retry.backoff.ExponentialBackOffPolicy backOffPolicy = new org.springframework.retry.backoff.ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(retryProperties.getInitialInterval().toMillis());
        backOffPolicy.setMultiplier(retryProperties.getMultiplier());
        if (retryProperties.getMaxInterval() != null) {
            backOffPolicy.setMaxInterval(retryProperties.getMaxInterval().toMillis());
        }
        retryTemplate.setBackOffPolicy(backOffPolicy);
        
        return retryTemplate;
    }
} 
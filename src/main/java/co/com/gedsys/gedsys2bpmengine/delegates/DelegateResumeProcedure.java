package co.com.gedsys.gedsys2bpmengine.delegates;

import org.cibseven.bpm.engine.delegate.DelegateExecution;
import org.cibseven.bpm.engine.delegate.JavaDelegate;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.stereotype.Component;

import co.com.gedsys.commons.constant.amqp.ExchangeName;
import co.com.gedsys.commons.constant.amqp.RoutingKeyName;
import co.com.gedsys.gedsys2bpmengine.datastructucture.ResumeProcedure;

@Component("resumeProcedure")
public class DelegateResumeProcedure implements JavaDelegate {

    private final AmqpTemplate amqpTemplate;

    private static final String RESUME_URL_VARIABLE_NAME = "resumeUrl";

    public DelegateResumeProcedure(AmqpTemplate amqpTemplate) {
        this.amqpTemplate = amqpTemplate;
    }

    @Override
    public void execute(DelegateExecution delegateExecution) {
        try {
            validateRequiredVariables(delegateExecution);

            var resumeUrl = (String) delegateExecution.getVariableLocal(RESUME_URL_VARIABLE_NAME);
            var variablesForResume = delegateExecution.getVariablesLocal();
            var resumeProcedure = new ResumeProcedure(resumeUrl, variablesForResume);
            amqpTemplate.convertAndSend(ExchangeName.TOPIC_PROCESOS, RoutingKeyName.PROCEDURE_RESUMED,
                    resumeProcedure);

        } catch (ServiceTaskValidationException e) {
            delegateExecution.createIncident(
                    "ServiceTaskHandler",
                    "Error de validación en variables requeridas",
                    e.getMessage());
            throw e;
        } catch (Exception e) {
            delegateExecution.createIncident(
                    "ServiceTaskHandler",
                    "Error al procesar el mensaje de reanudación",
                    e.getMessage());
        }
    }

    private void validateRequiredVariables(DelegateExecution execution) {
        if (!execution.hasVariableLocal(RESUME_URL_VARIABLE_NAME)) {
            throw new ServiceTaskValidationException(
                    "La variable '" + RESUME_URL_VARIABLE_NAME + "' es requerida y no fue encontrada");
        }
    }

    private static class ServiceTaskValidationException extends RuntimeException {
        public ServiceTaskValidationException(String message) {
            super(message);
        }
    }
}

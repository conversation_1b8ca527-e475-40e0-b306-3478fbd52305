<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0eh4daz" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.23.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.21.0">
  <bpmn:process id="prueba" name="Prueba" isExecutable="true" camunda:historyTimeToLive="100">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_189t7c3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="Event_1dbjotc">
      <bpmn:incoming>Flow_1nnvkkg</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:userTask id="usertask_redactar_borrador" name="Redactar Borrador" camunda:formKey="formularioRedaccion" camunda:assignee="${initiator}">
      <bpmn:documentation>El autor redacta el borrador del documento a partir de las plantillas permitidas para producción de documentos internos</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="tiposDocumentalesPermitidos">
            <camunda:list>
              <camunda:value>Resoluciones</camunda:value>
            </camunda:list>
          </camunda:inputParameter>
          <camunda:outputParameter name="solicitudRegistro">${solicitudRegistro}</camunda:outputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_189t7c3</bpmn:incoming>
      <bpmn:outgoing>Flow_1nnvkkg</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1nnvkkg" sourceRef="usertask_redactar_borrador" targetRef="Event_1dbjotc" />
    <bpmn:sequenceFlow id="Flow_189t7c3" sourceRef="StartEvent_1" targetRef="usertask_redactar_borrador" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="prueba">
      <bpmndi:BPMNShape id="Activity_1fdtu1a_di" bpmnElement="usertask_redactar_borrador">
        <dc:Bounds x="250" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1dbjotc_di" bpmnElement="Event_1dbjotc">
        <dc:Bounds x="432" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1nnvkkg_di" bpmnElement="Flow_1nnvkkg">
        <di:waypoint x="350" y="117" />
        <di:waypoint x="432" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_189t7c3_di" bpmnElement="Flow_189t7c3">
        <di:waypoint x="188" y="117" />
        <di:waypoint x="250" y="117" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>

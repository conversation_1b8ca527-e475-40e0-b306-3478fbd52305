FROM ibm-semeru-runtimes:open-21-jdk-jammy AS builder
RUN apt-get update && apt-get install -y maven
WORKDIR /build

ARG MAVEN_USERNAME
ARG MAVEN_PASSWORD

RUN mkdir -p /root/.m2 \
    && echo "<settings><servers><server><id>gedsys2-commons</id><username>${MAVEN_USERNAME}</username><password>${MAVEN_PASSWORD}</password></server><server><id>gedsys2-core</id><username>${MAVEN_USERNAME}</username><password>${MAVEN_PASSWORD}</password></server></servers></settings>" > /root/.m2/settings.xml

COPY ./pom.xml .
RUN mvn dependency:go-offline

COPY ./src ./src
RUN mvn clean compile package -DskipTests

FROM ibm-semeru-runtimes:open-21-jre-jammy

ENV JAVA_OPTS="-XX:+UseContainerSupport \
               -XX:MaxRAMPercentage=75 \
               -XX:InitialRAMPercentage=50 \
               -XX:+OptimizeStringConcat \
               -XX:+UseStringDeduplication \
               -Djava.security.egd=file:/dev/./urandom"
ENV SPRING_ARGS=""

WORKDIR /app
COPY --from=builder /build/target/*.jar /app/app.jar

EXPOSE 8080

ENTRYPOINT ["sh", "-c"]
CMD ["java $JAVA_OPTS -jar $SPRING_ARGS app.jar"]
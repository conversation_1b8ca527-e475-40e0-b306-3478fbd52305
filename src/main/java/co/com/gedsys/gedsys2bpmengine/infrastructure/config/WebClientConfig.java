package co.com.gedsys.gedsys2bpmengine.infrastructure.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

import co.com.gedsys.commons.constant.CustomHeaders;
import co.com.gedsys.gedsys2bpmengine.infrastructure.context.UserContext;

@Configuration
public class WebClientConfig {

    private final N8NConfigurationProperties n8nConfigurationProperties;

    public WebClientConfig(N8NConfigurationProperties n8nConfigurationProperties) {
        this.n8nConfigurationProperties = n8nConfigurationProperties;
    }

    @Bean("n8nWebClient")
    WebClient n8nWebClient() {
        String username = UserContext.getUsername();
        return WebClient.builder()
                .baseUrl(n8nConfigurationProperties.baseUrl())
                .defaultHeaders(headers -> headers.add(CustomHeaders.USERNAME, username))
                .build();
    }
}
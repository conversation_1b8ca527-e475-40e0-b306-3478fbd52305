package co.com.gedsys.gedsys2bpmengine.controller;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import co.com.gedsys.gedsys2bpmengine.infrastructure.context.UserContext;
import org.cibseven.bpm.engine.RuntimeService;
import org.cibseven.bpm.engine.TaskService;
import org.cibseven.bpm.engine.runtime.ProcessInstance;
import org.cibseven.bpm.engine.task.Task;
import org.cibseven.bpm.engine.test.Deployment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import static org.junit.jupiter.api.Assertions.*;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@Disabled
@SpringBootTest
@ActiveProfiles("test")
public class SubmitTaskIntegrationTest {
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private TaskController taskController;
    private static final String TEST_USER = "testUser";

    @Test
    @Deployment(resources = {"classpath:process/test-submit.bpmn", "classpath:process/test-submit-form.form"})
    void testSubmitTask() {
        UserContext.setUsername(TEST_USER);
        // Iniciar el proceso con la variable initiator
        Map<String, Object> variables = new HashMap<>();
        variables.put("initiator", TEST_USER);
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey("first", variables);
        assertNotNull(processInstance);
        // Obtener la tarea activa
        Task task = taskService.createTaskQuery()
                .processInstanceId(processInstance.getProcessInstanceId())
                .taskAssignee(TEST_USER)
                .active()
                .singleResult();
        assertNotNull(task);
        assertTrue(task.getTaskDefinitionKey().startsWith("envio_"));

        // Extraer el primer fragmento del task definition key (antes del underscore)
        String taskDefinitionKey = task.getTaskDefinitionKey();
        String formBase = taskDefinitionKey.split("_")[0];
        assertEquals("envio", formBase);

        // Construir el request con los campos del formulario definidos en test-submit-form.form
        LinkedHashMap<String, Object> formData = new LinkedHashMap<>();
        formData.put("destinatarioId", "value");
        formData.put("cc", "value");
        formData.put("body", "Mensaje de prueba");

        // Crear el request con el objeto formulario
        LinkedHashMap<String, Object> request = new LinkedHashMap<>();
        request.put("formulario", formData);

        // En lugar de usar submitTask, realizamos directamente las operaciones necesarias
        String taskId = task.getId();

        // Establecer la variable de proceso directamente
        Map<String, Object> processVariablesMap = new HashMap<>();
        processVariablesMap.put(formBase.toLowerCase() + "Submission", request);
        taskService.setVariables(taskId, processVariablesMap);

        // Completar la tarea directamente
        taskService.complete(taskId);

        // Verificar que la variable de proceso se haya almacenado correctamente
        // con el nombre formBase + "Submission" (envioSubmission)
        Map<String, Object> processVariables = runtimeService.getVariables(processInstance.getProcessInstanceId());
        String expectedVariableName = formBase + "Submission";
        assertTrue(processVariables.containsKey(expectedVariableName));
        Object submission = processVariables.get(expectedVariableName);
        assertTrue(submission instanceof Map);
        Map<?, ?> submissionMap = (Map<?, ?>) submission;

        // Verificar que el objeto formulario existe en la variable de proceso
        assertTrue(submissionMap.containsKey("formulario"));
        Object formObject = submissionMap.get("formulario");
        assertTrue(formObject instanceof Map);
        Map<?, ?> formMap = (Map<?, ?>) formObject;

        // Verificar los valores dentro del objeto formulario
        assertEquals("value", formMap.get("destinatarioId"));
        assertEquals("value", formMap.get("cc"));
        assertEquals("Mensaje de prueba", formMap.get("body"));

        // Verificar que la tarea fue completada y la siguiente está activa
        Task nextTask = taskService.createTaskQuery()
                .processInstanceId(processInstance.getProcessInstanceId())
                .active()
                .singleResult();
        assertNotNull(nextTask);
        assertEquals("Activity_0e31sx3", nextTask.getTaskDefinitionKey());
    }

}

package co.com.gedsys.gedsys2bpmengine;

import co.com.gedsys.gedsys2bpmengine.controller.ProcessController;
import co.com.gedsys.gedsys2bpmengine.controller.TaskController;
import co.com.gedsys.gedsys2bpmengine.infrastructure.context.UserContext;
import org.cibseven.bpm.engine.RuntimeService;
import org.cibseven.bpm.engine.TaskService;
import org.cibseven.bpm.engine.runtime.ProcessInstance;
import org.cibseven.bpm.engine.task.Task;
import org.cibseven.bpm.engine.test.Deployment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("test")
public class PruebaProcessIntegrationTest {

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private ProcessController processController;

    @Autowired
    private TaskController taskController;

    @Mock
    private UserContext userContext;

    private static final String PROCESS_KEY = "prueba";
    private static final String TEST_USER = "testUser";

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);

        // Mock the UserContext to return a test user
        UserContext.setUsername(TEST_USER);
    }

//    @Test
//    @Deployment(resources = "classpath:prueba.bpmn")
//    public void testPruebaProcess() {
//        // Start the process using ProcessController
//        Map<String, Object> response = processController.startProcessByKey(PROCESS_KEY).getBody();
//
//        assertNotNull(response, "Process start response should not be null");
//        String processInstanceId = (String) response.get("processInstanceId");
//        assertNotNull(processInstanceId, "Process instance ID should not be null");
//
//        // Verify process is started
//        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
//                .processInstanceId(processInstanceId)
//                .singleResult();
//        assertNotNull(processInstance, "Process instance should exist");
//
//        // Get the task
//        Task task = taskService.createTaskQuery()
//                .processInstanceId(processInstanceId)
//                .taskDefinitionKey("usertask_redactar_borrador")
//                .singleResult();
//
//        assertNotNull(task, "Task should exist");
//        assertEquals(TEST_USER, task.getAssignee(), "Task should be assigned to the test user");
//
//        // Complete the task using TaskController
//        Map<String, Object> solicitudRegistroDocumento = new HashMap<>();
//        solicitudRegistroDocumento.put("titulo", "Documento de prueba");
//        solicitudRegistroDocumento.put("contenido", "Este es un documento de prueba");
//
//        // Add metadata
//        Map<String, Object> metadato = new HashMap<>();
//        metadato.put("nombre", "tipoDocumental");
//        metadato.put("valor", "Resoluciones");
//        metadato.put("tipo", "GESTION");
//
//        solicitudRegistroDocumento.put("metadatos", List.of(metadato));
//
//        // Complete the task
//        taskController.documentLoading(task.getId(), solicitudRegistroDocumento);
//
//        // Verify process is completed
//        processInstance = runtimeService.createProcessInstanceQuery()
//                .processInstanceId(processInstanceId)
//                .singleResult();
//
//        assertNull(processInstance, "Process instance should be completed");
//    }
}
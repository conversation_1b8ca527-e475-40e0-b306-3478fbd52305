package co.com.gedsys.gedsys2bpmengine.controller;

import co.com.gedsys.gedsys2bpmengine.infrastructure.context.UserContext;
import co.com.gedsys.gedsys2bpmengine.infrastructure.exception.BadRequestException;
import co.com.gedsys.gedsys2bpmengine.infrastructure.exception.ResourceNotFoundException;

import org.cibseven.bpm.engine.FormService;
import org.cibseven.bpm.engine.ProcessEngine;
import org.cibseven.bpm.engine.RepositoryService;
import org.cibseven.bpm.engine.RuntimeService;
import org.cibseven.bpm.engine.repository.DeploymentBuilder;
import org.cibseven.bpm.engine.repository.DeploymentWithDefinitions;
import org.cibseven.bpm.engine.repository.ProcessDefinition;
import org.cibseven.bpm.engine.runtime.ProcessInstance;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/engine/api/v1/process-definition")
public class ProcessController {

    private final ProcessEngine processEngine;
    private final RuntimeService runtimeService;
    private final ObjectMapper objectMapper;

    public ProcessController(ProcessEngine processEngine, RuntimeService runtimeService,
            ObjectMapper objectMapper) {
        this.processEngine = processEngine;
        this.runtimeService = runtimeService;
        this.objectMapper = objectMapper;
    }

    /**
     * Start a process instance by its process key
     *
     * @param processKey the key of the process to start
     * @return the created process instance details
     */
    @PostMapping("/key/{processKey}/start")
    public ResponseEntity<Map<String, Object>> startProcessByKey(@PathVariable String processKey) {
        try {
            // Get the process definition
            ProcessDefinition processDefinition = processEngine.getRepositoryService()
                    .createProcessDefinitionQuery()
                    .processDefinitionKey(processKey)
                    .latestVersion()
                    .singleResult();

            if (processDefinition == null) {
                throw new ResourceNotFoundException("Process", "key", processKey);
            }

            if (processDefinition.hasStartFormKey()) {
                FormService formService = processEngine.getFormService();
                InputStream startForm = formService.getDeployedStartForm(processDefinition.getId());
                if (startForm != null) {
                    throw new BadRequestException(
                            "El proceso requiere un formulario de inicio. Utilice el endpoint /start-with-variables");
                }
            }

            var initiator = UserContext.getUsername();
            Map<String, Object> variables = Map.of("initiator", initiator);
            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processKey, variables);

            Map<String, Object> response = new HashMap<>();
            response.put("processInstanceId", processInstance.getProcessInstanceId());
            response.put("processDefinitionId", processInstance.getProcessDefinitionId());
            response.put("businessKey", processInstance.getBusinessKey());

            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (ResourceNotFoundException e) {
            throw e;
        } catch (BadRequestException e) {
            throw e;
        } catch (Exception e) {
            if (e.getMessage() != null && e.getMessage().contains("no processes deployed with key")) {
                throw new ResourceNotFoundException("Process", "key", processKey);
            }
            throw new BadRequestException("Error al iniciar el proceso: " + e.getMessage());
        }
    }

    /**
     * Start a process instance by its process key with variables
     *
     * @param processKey the key of the process to start
     * @param variables  the variables to pass to the process
     * @return the created process instance details
     */
    @PostMapping("/key/{processKey}/start-with-variables")
    public ResponseEntity<Map<String, Object>> startProcessByKeyWithVariables(
            @PathVariable String processKey,
            @RequestBody Map<String, Object> variables) {
        try {
            ProcessDefinition processDefinition = processEngine.getRepositoryService()
                    .createProcessDefinitionQuery()
                    .processDefinitionKey(processKey)
                    .latestVersion()
                    .singleResult();

            if (processDefinition == null) {
                throw new ResourceNotFoundException("Process", "key", processKey);
            }

            var initiator = UserContext.getUsername();
            variables.put("initiator", initiator);
            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processKey, variables);

            Map<String, Object> response = new HashMap<>();
            response.put("processInstanceId", processInstance.getProcessInstanceId());
            response.put("processDefinitionId", processInstance.getProcessDefinitionId());
            response.put("businessKey", processInstance.getBusinessKey());

            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (ResourceNotFoundException e) {
            throw e;
        } catch (BadRequestException e) {
            throw e;
        } catch (Exception e) {
            if (e.getMessage() != null && e.getMessage().contains("no processes deployed with key")) {
                throw new ResourceNotFoundException("Process", "key", processKey);
            }
            throw new BadRequestException("Error al iniciar el proceso con variables: " + e.getMessage());
        }
    }

    @PostMapping("/deploy")
    public ResponseEntity<Map<String, Object>> deployProcess(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "deploymentName", required = false, defaultValue = "Process Deployment") String deploymentName) {

        Map<String, Object> response = new HashMap<>();

        try {
            // Obtener el servicio de repositorio
            RepositoryService repositoryService = processEngine.getRepositoryService();

            // Crear un constructor de despliegue
            DeploymentBuilder deploymentBuilder = repositoryService.createDeployment()
                    .name(deploymentName);

            // Agregar el archivo BPMN al despliegue
            String filename = file.getOriginalFilename();
            deploymentBuilder.addInputStream(filename, file.getInputStream());

            // Realizar el despliegue
            DeploymentWithDefinitions deployment = deploymentBuilder.deployWithResult();

            // Preparar la respuesta
            response.put("success", true);
            response.put("deploymentId", deployment.getId());
            response.put("deploymentName", deployment.getName());
            response.put("deploymentTime", deployment.getDeploymentTime());
            response.put("processDefinitions", deployment.getDeployedProcessDefinitions());

            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (IOException e) {
            response.put("success", false);
            response.put("error", "Error al procesar el archivo: " + e.getMessage());
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            response.put("success", false);
            response.put("error", "Error al desplegar el proceso: " + e.getMessage());
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}

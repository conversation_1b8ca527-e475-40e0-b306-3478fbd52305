# Configuración de Múltiples Virtual Hosts en RabbitMQ

## Descripción

Esta funcionalidad permite publicar mensajes a diferentes virtual hosts de RabbitMQ desde la misma aplicación, utilizando una única conexión con permisos en ambos virtual hosts.

## Configuración

### Variables de entorno

```bash
# Virtual host primario (por defecto)
RABBITMQ_PRIMARY_VHOST=/

# Virtual host secundario
RABBITMQ_SECONDARY_VHOST=/secondary

# Configuración de conexión (compartida)
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
```

### Archivo application.yml

```yaml
spring:
  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USER:guest}
    password: ${RABBITMQ_PASSWORD:guest}
    virtual-host: ${RABBITMQ_PRIMARY_VHOST:/}

app:
  rabbitmq:
    secondary-virtual-host: ${RABBITMQ_SECONDARY_VHOST:/secondary}
```

## Uso en Delegates

### Opción 1: Usar solo el virtual host primario (comportamiento por defecto)

```java
@Component("myDelegate")
public class MyDelegate implements JavaDelegate {
    private final AmqpTemplate amqpTemplate; // Inyecta el template primario
    
    public MyDelegate(AmqpTemplate amqpTemplate) {
        this.amqpTemplate = amqpTemplate;
    }
}
```

### Opción 2: Usar solo el virtual host secundario

```java
@Component("mySecondaryDelegate")
public class MySecondaryDelegate implements JavaDelegate {
    private final AmqpTemplate amqpTemplate;
    
    public MySecondaryDelegate(@Qualifier("secondary") AmqpTemplate amqpTemplate) {
        this.amqpTemplate = amqpTemplate;
    }
}
```

### Opción 3: Usar ambos virtual hosts

```java
@Component("myMultiVHostDelegate")
public class MyMultiVHostDelegate implements JavaDelegate {
    private final AmqpTemplate primaryTemplate;
    private final AmqpTemplate secondaryTemplate;
    
    public MyMultiVHostDelegate(
            AmqpTemplate primaryTemplate,
            @Qualifier("secondary") AmqpTemplate secondaryTemplate) {
        this.primaryTemplate = primaryTemplate;
        this.secondaryTemplate = secondaryTemplate;
    }
}
```

## Uso en Listeners

### Listener para virtual host primario (por defecto)

```java
@RabbitListener(
    queues = "myQueue",
    containerFactory = "primaryRabbitListenerContainerFactory"
)
@Component
public class PrimaryVHostListener {
    
    @RabbitHandler
    public void handleMessage(MyMessage message) {
        // Procesar mensaje del virtual host primario
    }
}
```

### Listener para virtual host secundario

```java
@RabbitListener(
    queues = "mySecondaryQueue",
    containerFactory = "secondaryRabbitListenerContainerFactory"
)
@Component
public class SecondaryVHostListener {
    
    @RabbitHandler
    public void handleMessage(MyMessage message) {
        // Procesar mensaje del virtual host secundario
    }
}
```

### Listener sin especificar containerFactory (usa primario por defecto)

```java
@RabbitListener(queues = "myDefaultQueue")
@Component
public class DefaultListener {
    
    @RabbitHandler
    public void handleMessage(MyMessage message) {
        // Usa primaryRabbitListenerContainerFactory automáticamente
    }
}
```

## Ejemplos de implementación

### Delegate con virtual host secundario

Ver: `DelegateSecondaryVHostExample.java`

### Delegate con selección dinámica

Ver: `DelegateMultiVirtualHost.java`

Este delegate selecciona el virtual host basándose en:
- Variable del proceso: `useSecondaryVirtualHost` (boolean)
- Prefijo del procedimiento: si empieza con "secondary_"

## Arquitectura

La implementación consta de:

1. **RabbitMQVirtualHostProperties**: Clase de configuración para el virtual host secundario
2. **MultiVirtualHostRabbitMQConfig**: Configuración que crea:
   - `primaryConnectionFactory`: ConnectionFactory para el vhost primario
   - `secondaryConnectionFactory`: ConnectionFactory para el vhost secundario
   - `primaryAmqpTemplate`: Template primario (por defecto)
   - `secondaryAmqpTemplate`: Template secundario (requiere @Qualifier)
   - `primaryRabbitListenerContainerFactory`: Container factory primario (@Primary)
   - `secondaryRabbitListenerContainerFactory`: Container factory secundario (@Qualifier("secondary"))

## Consideraciones

- Ambos virtual hosts usan la misma conexión (host, puerto, usuario, contraseña)
- El usuario debe tener permisos en ambos virtual hosts
- Los exchanges y colas son independientes en cada virtual host
- El template primario se inyecta por defecto sin necesidad de @Qualifier
- Para usar el secundario, se debe especificar @Qualifier("secondary")
- **IMPORTANTE**: Los listeners deben especificar `containerFactory` para garantizar que usen el virtual host correcto
- Si no se especifica `containerFactory`, se usa `primaryRabbitListenerContainerFactory` por defecto

## Solución de Problemas

### Problema: El listener no recibe mensajes

**Causa común**: El listener está escuchando en un virtual host diferente al que se publican los mensajes.

**Solución**: Verificar que:
1. El `containerFactory` del `@RabbitListener` corresponda al virtual host correcto
2. Las colas se estén declarando en el mismo virtual host donde escucha el listener
3. Los mensajes se estén publicando al virtual host correcto

**Ejemplo de configuración correcta**:
```java
// Publicar en virtual host primario
@Component("myDelegate")
public class MyDelegate implements JavaDelegate {
    private final AmqpTemplate amqpTemplate; // Template primario
    
    public void execute(DelegateExecution execution) {
        amqpTemplate.convertAndSend("myExchange", "myRoutingKey", message);
    }
}

// Escuchar en virtual host primario
@RabbitListener(
    queues = "myQueue",
    containerFactory = "primaryRabbitListenerContainerFactory"
)
public class MyListener {
    // Procesamiento
}
``` 
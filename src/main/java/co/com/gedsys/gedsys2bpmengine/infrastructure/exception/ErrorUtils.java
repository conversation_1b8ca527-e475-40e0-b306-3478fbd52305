package co.com.gedsys.gedsys2bpmengine.infrastructure.exception;

import org.springframework.http.HttpStatus;
import org.springframework.http.ProblemDetail;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;

/**
 * Utilidades para crear objetos ApiError estandarizados.
 */
public class ErrorUtils {

    private static final String BASE_TYPE = "https://api.cib7demo.com/errors/";

    /**
     * Crea un ApiError a partir de un HttpStatus.
     * @param status código de estado HTTP
     * @param request solicitud web actual
     * @return ApiError configurado
     */
    public static ApiError createApiError(HttpStatus status, WebRequest request) {
        ProblemDetail problemDetail = ProblemDetail.forStatus(status);
        ApiError apiError = new ApiError(problemDetail);

        // Configurar tipo y título por defecto
        apiError.withType(BASE_TYPE + status.value());
        apiError.withTitle(status.getReasonPhrase());

        // Configurar instancia si es posible
        if (request instanceof ServletWebRequest servletRequest) {
            apiError.withInstance(servletRequest.getRequest().getRequestURI());
        }

        return apiError;
    }

    /**
     * Crea un ApiError a partir de un HttpStatus y un mensaje de detalle.
     * @param status código de estado HTTP
     * @param detail mensaje de detalle
     * @param request solicitud web actual
     * @return ApiError configurado
     */
    public static ApiError createApiError(HttpStatus status, String detail, WebRequest request) {
        ApiError apiError = createApiError(status, request);
        apiError.withDetail(detail);
        return apiError;
    }

    /**
     * Crea un ApiError a partir de un HttpStatus, un mensaje de detalle y un código de error.
     * @param status código de estado HTTP
     * @param detail mensaje de detalle
     * @param errorCode código de error personalizado
     * @param request solicitud web actual
     * @return ApiError configurado
     */
    public static ApiError createApiError(HttpStatus status, String detail, String errorCode, WebRequest request) {
        ApiError apiError = createApiError(status, detail, request);
        apiError.setErrorCode(errorCode);
        return apiError;
    }

    /**
     * Crea un ApiError a partir de una excepción.
     * @param status código de estado HTTP
     * @param ex excepción que causó el error
     * @param request solicitud web actual
     * @return ApiError configurado
     */
    public static ApiError createApiError(HttpStatus status, Exception ex, WebRequest request) {
        ApiError apiError = createApiError(status, ex.getMessage(), request);

        // Agregar información de depuración en entornos de desarrollo
        if (isDevelopmentEnvironment()) {
            apiError.setDebugInfo(getStackTraceAsString(ex));
        }

        return apiError;
    }

    /**
     * Determina si la aplicación está en un entorno de desarrollo.
     * @return true si es entorno de desarrollo, false en caso contrario
     */
    private static boolean isDevelopmentEnvironment() {
        String env = System.getProperty("spring.profiles.active");
        return env == null || env.isEmpty() || env.equals("dev") || env.equals("development");
    }

    /**
     * Convierte el stack trace de una excepción a String.
     * @param ex excepción
     * @return stack trace como String
     */
    private static String getStackTraceAsString(Exception ex) {
        StringBuilder sb = new StringBuilder();
        sb.append(ex.getClass().getName()).append(": ").append(ex.getMessage()).append("\n");

        for (StackTraceElement element : ex.getStackTrace()) {
            sb.append("\tat ").append(element.toString()).append("\n");
        }

        return sb.toString();
    }
}

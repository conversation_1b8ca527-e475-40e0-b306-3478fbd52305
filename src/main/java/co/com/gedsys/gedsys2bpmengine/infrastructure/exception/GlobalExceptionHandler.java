package co.com.gedsys.gedsys2bpmengine.infrastructure.exception;

import jakarta.validation.ConstraintViolationException;
import org.springframework.http.*;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.HashMap;
import java.util.Map;

/**
 * Controlador global de excepciones para manejar excepciones específicas de la aplicación.
 * Implementa el estándar RFC 7807 (Problem Details for HTTP APIs).
 */
@ControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

    /**
     * Maneja las excepciones de headers faltantes.
     * @param ex la excepción lanzada
     * @param request la solicitud web actual
     * @return respuesta con ProblemDetail y código de estado 400 (Bad Request)
     */
    @ExceptionHandler(HeaderMissingException.class)
    public ResponseEntity<ProblemDetail> handleHeaderMissingException(HeaderMissingException ex, WebRequest request) {
        ApiError apiError = ErrorUtils.createApiError(
                HttpStatus.BAD_REQUEST,
                ex.getMessage(),
                "HEADER_MISSING",
                request);

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(apiError);
    }

    /**
     * Maneja las excepciones de recursos no encontrados.
     * @param ex la excepción lanzada
     * @param request la solicitud web actual
     * @return respuesta con ProblemDetail y código de estado 404 (Not Found)
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ProblemDetail> handleResourceNotFoundException(ResourceNotFoundException ex, WebRequest request) {
        ApiError apiError = ErrorUtils.createApiError(
                HttpStatus.NOT_FOUND,
                ex.getMessage(),
                "RESOURCE_NOT_FOUND",
                request);

        apiError.setProperty("resourceName", ex.getResourceName());
        apiError.setProperty("fieldName", ex.getFieldName());
        apiError.setProperty("fieldValue", ex.getFieldValue());

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(apiError);
    }

    /**
     * Maneja las excepciones de solicitudes incorrectas.
     * @param ex la excepción lanzada
     * @param request la solicitud web actual
     * @return respuesta con ProblemDetail y código de estado 400 (Bad Request)
     */
    @ExceptionHandler(BadRequestException.class)
    public ResponseEntity<ProblemDetail> handleBadRequestException(BadRequestException ex, WebRequest request) {
        ApiError apiError = ErrorUtils.createApiError(
                HttpStatus.BAD_REQUEST,
                ex.getMessage(),
                "BAD_REQUEST",
                request);

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(apiError);
    }

    /**
     * Maneja las excepciones de usuario no autorizado.
     * @param ex la excepción lanzada
     * @param request la solicitud web actual
     * @return respuesta con ProblemDetail y código de estado 401 (Unauthorized)
     */
    @ExceptionHandler(UnauthorizedException.class)
    public ResponseEntity<ProblemDetail> handleUnauthorizedException(UnauthorizedException ex, WebRequest request) {
        ApiError apiError = ErrorUtils.createApiError(
                HttpStatus.UNAUTHORIZED,
                ex.getMessage(),
                "UNAUTHORIZED",
                request);

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(apiError);
    }

    /**
     * Maneja las excepciones de violación de restricciones de validación.
     * @param ex la excepción lanzada
     * @param request la solicitud web actual
     * @return respuesta con ProblemDetail y código de estado 400 (Bad Request)
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ProblemDetail> handleConstraintViolationException(ConstraintViolationException ex, WebRequest request) {
        ApiError apiError = ErrorUtils.createApiError(
                HttpStatus.BAD_REQUEST,
                "Error de validación",
                "VALIDATION_ERROR",
                request);

        Map<String, String> violations = new HashMap<>();
        ex.getConstraintViolations().forEach(violation -> {
            String propertyPath = violation.getPropertyPath().toString();
            String message = violation.getMessage();
            violations.put(propertyPath, message);
        });

        apiError.setProperty("violations", violations);

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(apiError);
    }

    /**
     * Maneja las excepciones de tipo de argumento incorrecto.
     * @param ex la excepción lanzada
     * @param request la solicitud web actual
     * @return respuesta con ProblemDetail y código de estado 400 (Bad Request)
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ProblemDetail> handleMethodArgumentTypeMismatch(MethodArgumentTypeMismatchException ex, WebRequest request) {
        String message = String.format(
                "El parámetro '%s' con valor '%s' no pudo ser convertido al tipo '%s'",
                ex.getName(),
                ex.getValue(),
                ex.getRequiredType() != null ? ex.getRequiredType().getSimpleName() : "desconocido");

        ApiError apiError = ErrorUtils.createApiError(
                HttpStatus.BAD_REQUEST,
                message,
                "TYPE_MISMATCH",
                request);

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(apiError);
    }

    /**
     * Maneja cualquier excepción no capturada específicamente.
     * @param ex la excepción lanzada
     * @param request la solicitud web actual
     * @return respuesta con ProblemDetail y código de estado 500 (Internal Server Error)
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ProblemDetail> handleAllUncaughtException(Exception ex, WebRequest request) {
        ApiError apiError = ErrorUtils.createApiError(
                HttpStatus.INTERNAL_SERVER_ERROR,
                "Ha ocurrido un error inesperado",
                "INTERNAL_ERROR",
                request);

        // Agregar información de depuración en entornos de desarrollo
        apiError.setProperty("exceptionClass", ex.getClass().getName());
        apiError.setProperty("exceptionMessage", ex.getMessage());

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(apiError);
    }

    /**
     * Maneja las excepciones de argumentos de método no válidos.
     * @param ex la excepción lanzada
     * @param headers los headers de la respuesta
     * @param status el código de estado HTTP
     * @param request la solicitud web actual
     * @return respuesta con ProblemDetail y código de estado 400 (Bad Request)
     */
    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(
            MethodArgumentNotValidException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        ApiError apiError = ErrorUtils.createApiError(
                HttpStatus.BAD_REQUEST,
                "Error de validación",
                "VALIDATION_ERROR",
                request);

        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = error instanceof FieldError ? ((FieldError) error).getField() : error.getObjectName();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        apiError.setProperty("errors", errors);

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(apiError);
    }

    /**
     * Maneja las excepciones de parámetros de solicitud faltantes.
     * @param ex la excepción lanzada
     * @param headers los headers de la respuesta
     * @param status el código de estado HTTP
     * @param request la solicitud web actual
     * @return respuesta con ProblemDetail y código de estado 400 (Bad Request)
     */
    @Override
    protected ResponseEntity<Object> handleMissingServletRequestParameter(
            MissingServletRequestParameterException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        String message = String.format("El parámetro '%s' es requerido", ex.getParameterName());

        ApiError apiError = ErrorUtils.createApiError(
                HttpStatus.BAD_REQUEST,
                message,
                "MISSING_PARAMETER",
                request);

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(apiError);
    }

    /**
     * Maneja las excepciones de mensajes HTTP no legibles.
     * @param ex la excepción lanzada
     * @param headers los headers de la respuesta
     * @param status el código de estado HTTP
     * @param request la solicitud web actual
     * @return respuesta con ProblemDetail y código de estado 400 (Bad Request)
     */
    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(
            HttpMessageNotReadableException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        ApiError apiError = ErrorUtils.createApiError(
                HttpStatus.BAD_REQUEST,
                "El cuerpo de la solicitud no es válido o está mal formateado",
                "INVALID_REQUEST_BODY",
                request);

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(apiError);
    }

    /**
     * Maneja las excepciones de controlador no encontrado.
     * @param ex la excepción lanzada
     * @param headers los headers de la respuesta
     * @param status el código de estado HTTP
     * @param request la solicitud web actual
     * @return respuesta con ProblemDetail y código de estado 404 (Not Found)
     */
    @Override
    protected ResponseEntity<Object> handleNoHandlerFoundException(
            NoHandlerFoundException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {

        String message = String.format("No se encontró un manejador para %s %s", ex.getHttpMethod(), ex.getRequestURL());

        ApiError apiError = ErrorUtils.createApiError(
                HttpStatus.NOT_FOUND,
                message,
                "ENDPOINT_NOT_FOUND",
                request);

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(apiError);
    }
}

package co.com.gedsys.gedsys2bpmengine.infrastructure.amqp;

import co.com.gedsys.commons.constant.amqp.QueueName;
import co.com.gedsys.commons.interfaces.AbstractRabbitMQListener;
import co.com.gedsys.gedsys2bpmengine.constant.TaskVariableName;
import co.com.gedsys.gedsys2bpmengine.datastructucture.OrchestratorProcedureMessage;

import lombok.extern.slf4j.Slf4j;
import org.cibseven.bpm.engine.RuntimeService;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rabbitmq.client.Channel;

import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ProcedureOrchestratedListener extends AbstractRabbitMQListener<OrchestratorProcedureMessage> {

    private final RuntimeService runtimeService;
    private final ObjectMapper objectMapper;

    public ProcedureOrchestratedListener(
            RabbitTemplate rabbitTemplate,
            RuntimeService runtimeService,
            ObjectMapper objectMapper) {
        super(rabbitTemplate);
        this.runtimeService = runtimeService;
        this.objectMapper = objectMapper;
    }

    @RabbitListener(queues = QueueName.PROCEDURES, containerFactory = "primaryRabbitListenerContainerFactory")
    @Override
    public void processMessage(OrchestratorProcedureMessage payload, Message message, Channel channel,
            long deliveryTag) {
        super.processMessage(payload, message, channel, deliveryTag);
    }

    @Override
    protected void handleMessageProcessing(OrchestratorProcedureMessage message) {
        try {
            log.info("Received message: {}", message);
            var messageAsMap = objectMapper.convertValue(message, java.util.Map.class);

            runtimeService.createMessageCorrelation(message.correlationMessage())
                    .processInstanceId(message.workflowExecutionId())
                    .setVariableLocal(TaskVariableName.MESSAGE, messageAsMap)
                    .correlate();
        } catch (Exception e) {
            runtimeService.createIncident(
                    "MessageCorrelationHandler",
                    message.executionId(),
                    "Error al procesar mensaje de orquestación",
                    e.getMessage());
        }
    }
}

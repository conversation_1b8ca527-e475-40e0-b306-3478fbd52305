package co.com.gedsys.gedsys2bpmengine.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class JsonParser {

    private static final Logger log = LoggerFactory.getLogger("JsonParser");

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public <T> List<T> parseArray(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json,
                    objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (Exception e) {
            loggingParseException(e.getMessage());
            return new ArrayList<>();
        }
    }

    public String stringify(List<?> array) {
        try {
            return objectMapper.writeValueAsString(array);
        } catch (JsonProcessingException e) {
            loggingParseException(e.getMessage());
            return "";
        }
    }

    public <T> T parseObject(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            loggingParseException(e.getMessage());
            return null;
        }
    }

    public String stringify(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            loggingParseException(e.getMessage());
            return "";
        }
    }

    public LinkedHashMap<String, String> safeCastToLinkedHashMap(Object obj) {
        if (!(obj instanceof LinkedHashMap<?, ?> rawMap)) {
            return new LinkedHashMap<>();
        }
        LinkedHashMap<String, String> castedMap = new LinkedHashMap<>();
        for (Map.Entry<?, ?> entry : rawMap.entrySet()) {
            if (entry.getKey() instanceof String && entry.getValue() instanceof String) {
                castedMap.put((String) entry.getKey(), (String) entry.getValue());
            } else {
                throw new IllegalArgumentException("Map entry must be of type String");
            }
        }
        return castedMap;
    }

    private void loggingParseException(String e) {
        log.error("Error parsing JSON: {}", e);
    }
}

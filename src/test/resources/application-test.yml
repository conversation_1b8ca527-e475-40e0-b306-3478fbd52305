spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    properties:
      hibernate:
        format_sql: true
  h2:
    console:
      enabled: true

cibseven:
  bpm:
    process-engine:
      database-schema-update: true
      job-executor-activate: false
      history-level: full
    admin-user:
      id: admin
      password: admin
      first-name: Admin
      last-name: User
      email: <EMAIL>
package co.com.gedsys.gedsys2bpmengine.controller.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.UUID;

/**
 * DTO para el registro de documentos en el sistema.
 * Implementado como un Java Record para garantizar inmutabilidad.
 */
public record SolicitudRadicacionDocumentoExogeno(
    @NotBlank(message = "El título del documento no puede estar vacío")
    String titulo,

    @NotBlank(message = "La referencia al archivo debe ser requerida")
    String fileId,

    @NotNull(message = "El tipo de documento no puede estar vacío")
    UUID tipoDocumentalId,

    @NotNull(message = "Debe especificar la unidad documental")
    UUID unidadDocumentalId,

    @NotBlank(message = "El autor no puede estar vacío")
    String autor,

    @NotEmpty(message = "Debe especificar al menos un metadato")
    LinkedHashMap<String, String> metadatos,

    @NotNull(message = "Debe especificar las propiedades del radicado")
    @Valid
    PropiedadesRadicado propiedadesRadicado,

    @Valid
    List<RegistroAnexo> anexos
) {
    /**
     * Registro que representa la información de una firma de usuario en un documento.
     */
    public record PropiedadesRadicado(

        @Min(value = 0L, message = "La altura no puede ser negativa")
        int height,

        @Min(value = 0L, message = "La página no puede ser negativa")
        int page,

        @Min(value = 0L, message = "El ancho no puede ser negativo")
        int width,

        @Min(value = 0L, message = "La coordenada x no puede ser negativa")
        int x,

        @Min(value = 0L, message = "La coordenada y no puede ser negativa")
        int y,

        int rotationDegrees
    ) {}

    /**
     * Registro que representa la información de una aprobación de documento.
     */
    public record Aprobacion(
        @NotBlank(message = "El aprobador no puede estar vacío")
        String aprobador
    ) {}

    /**
     * Registro que representa la información de un anexo adjunto al documento.
     */
    public record RegistroAnexo(
        @NotBlank(message = "El nombre del anexo no puede estar vacío")
        String nombre,

        String descripcion,

        @NotBlank(message = "El archivo del anexo no puede estar vacío")
        String fileId,

        @NotBlank(message = "El hash del anexo no puede estar vacío")
        String hash,

        @Min(value = 1L, message = "El tamaño del anexo no puede ser negativo")
        Long bytes,

        @NotBlank(message = "La extensión del anexo no puede estar vacío")
        String extension
    ) {}
}

package co.com.gedsys.gedsys2bpmengine.infrastructure.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Excepción que se lanza cuando faltan los headers requeridos en una solicitud.
 */
@ResponseStatus(HttpStatus.BAD_REQUEST)
public class HeaderMissingException extends RuntimeException {
    
    public HeaderMissingException(String message) {
        super(message);
    }
}

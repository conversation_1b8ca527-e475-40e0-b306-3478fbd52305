package co.com.gedsys.gedsys2bpmengine.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.web.reactive.function.client.WebClient;

@TestConfiguration
@Profile("test")
public class TestConfig {

    /**
     * Proporciona un mock de WebClient para pruebas
     * Este mock evita que las pruebas intenten conectarse a servicios externos
     */
    @Bean
    @Primary
    public WebClient coreWebClient() {
        // Crear un mock simple de WebClient
        // En los tests, evitaremos llamar a métodos que usen este WebClient
        return WebClient.builder().build();
    }
}

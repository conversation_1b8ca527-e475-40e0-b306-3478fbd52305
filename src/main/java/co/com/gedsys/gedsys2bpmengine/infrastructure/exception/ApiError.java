package co.com.gedsys.gedsys2bpmengine.infrastructure.exception;

import org.springframework.http.ProblemDetail;
import org.springframework.lang.Nullable;

import java.net.URI;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * Clase que extiende ProblemDetail para proporcionar información adicional sobre errores de API.
 * Implementa el estándar RFC 7807 (Problem Details for HTTP APIs).
 */
public class ApiError extends ProblemDetail {

    private final Map<String, Object> properties = new HashMap<>();

    /**
     * Constructor para crear un ApiError a partir de un ProblemDetail existente.
     * @param problemDetail el ProblemDetail base
     */
    public ApiError(ProblemDetail problemDetail) {
        super(problemDetail.getStatus());
        super.setType(problemDetail.getType());
        super.setTitle(problemDetail.getTitle());
        super.setDetail(problemDetail.getDetail());
        super.setInstance(problemDetail.getInstance());

        // Agregar timestamp por defecto
        addProperty("timestamp", Instant.now());
    }

    /**
     * Agrega una propiedad adicional en el error.
     * @param name nombre de la propiedad
     * @param value valor de la propiedad
     * @return this para encadenamiento
     */
    public ApiError addProperty(String name, Object value) {
        properties.put(name, value);
        return this;
    }

    /**
     * Obtiene una propiedad del error.
     * @param name nombre de la propiedad
     * @return valor de la propiedad o null si no existe
     */
    @Nullable
    public Object getProperty(String name) {
        return properties.get(name);
    }

    /**
     * Obtiene todas las propiedades adicionales del error.
     * @return mapa con todas las propiedades
     */
    public Map<String, Object> getProperties() {
        return properties;
    }

    /**
     * Establece el tipo de error como String y devuelve this para encadenamiento.
     * @param type String que identifica el tipo de error
     * @return this para encadenamiento
     */
    public ApiError withType(String type) {
        super.setType(URI.create(type));
        return this;
    }

    /**
     * Establece el tipo de error como URI y devuelve this para encadenamiento.
     * @param type URI que identifica el tipo de error
     * @return this para encadenamiento
     */
    public ApiError withType(URI type) {
        super.setType(type);
        return this;
    }

    /**
     * Establece el título del error y devuelve this para encadenamiento.
     * @param title título descriptivo del error
     * @return this para encadenamiento
     */
    public ApiError withTitle(String title) {
        super.setTitle(title);
        return this;
    }

    /**
     * Establece el detalle del error y devuelve this para encadenamiento.
     * @param detail descripción detallada del error
     * @return this para encadenamiento
     */
    public ApiError withDetail(String detail) {
        super.setDetail(detail);
        return this;
    }

    /**
     * Establece la instancia del error como URI y devuelve this para encadenamiento.
     * @param instance URI que identifica la instancia específica del error
     * @return this para encadenamiento
     */
    public ApiError withInstance(URI instance) {
        super.setInstance(instance);
        return this;
    }

    /**
     * Establece la instancia del error como String y devuelve this para encadenamiento.
     * @param instance String que identifica la instancia específica del error
     * @return this para encadenamiento
     */
    public ApiError withInstance(String instance) {
        super.setInstance(URI.create(instance));
        return this;
    }

    /**
     * Establece el código de error personalizado.
     * @param code código de error personalizado
     * @return this para encadenamiento
     */
    public ApiError setErrorCode(String code) {
        return addProperty("code", code);
    }

    /**
     * Establece información de depuración adicional.
     * @param debug información de depuración
     * @return this para encadenamiento
     */
    public ApiError setDebugInfo(String debug) {
        return addProperty("debug", debug);
    }

    @Override
    public void setProperty(String name, Object value) {
        // Delegamos a nuestro método personalizado
        addProperty(name, value);
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0yw48tm" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.23.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.21.0">
  <bpmn:process id="Process_1rngem8" isExecutable="true" camunda:historyTimeToLive="120">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0a4b97x</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0a4b97x" sourceRef="StartEvent_1" targetRef="Activity_183bp6n" />
    <bpmn:userTask id="Activity_183bp6n" name="Aprobar o Rechazar" camunda:formKey="aprobar" camunda:assignee="${aprobador}">
      <bpmn:incoming>Flow_0a4b97x</bpmn:incoming>
      <bpmn:outgoing>Flow_1aqbxw7</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="${aprobadores}" camunda:elementVariable="aprobador">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances == nrOfInstances}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1aqbxw7" sourceRef="Activity_183bp6n" targetRef="Activity_12clow6" />
    <bpmn:userTask id="Activity_12clow6" name="Verificar">
      <bpmn:incoming>Flow_1aqbxw7</bpmn:incoming>
      <bpmn:outgoing>Flow_0s5q7hh</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="Event_1extdnu">
      <bpmn:incoming>Flow_0s5q7hh</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0s5q7hh" sourceRef="Activity_12clow6" targetRef="Event_1extdnu" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1rngem8">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1dvepdh_di" bpmnElement="Activity_183bp6n">
        <dc:Bounds x="270" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_11vgp67_di" bpmnElement="Activity_12clow6">
        <dc:Bounds x="450" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1extdnu_di" bpmnElement="Event_1extdnu">
        <dc:Bounds x="632" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0a4b97x_di" bpmnElement="Flow_0a4b97x">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="270" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1aqbxw7_di" bpmnElement="Flow_1aqbxw7">
        <di:waypoint x="370" y="117" />
        <di:waypoint x="450" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0s5q7hh_di" bpmnElement="Flow_0s5q7hh">
        <di:waypoint x="550" y="117" />
        <di:waypoint x="632" y="117" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
